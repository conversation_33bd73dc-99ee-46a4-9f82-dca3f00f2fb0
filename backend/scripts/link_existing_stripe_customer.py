#!/usr/bin/env python3
"""
<PERSON>ript to link an existing Stripe customer to a user account and upgrade them to pro.
This script will:
1. Find the user by email
2. Link them to the provided Stripe customer ID
3. Create a free pro subscription for them
"""

import asyncio
import os
import sys
from datetime import datetime, timezone
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

import stripe
from services.supabase import DBConnection
from utils.logger import logger
from utils.config import config

# Initialize Stripe
stripe.api_key = config.STRIPE_SECRET_KEY


async def find_user_by_email(client, email: str):
    """Find user in the database by email address."""
    try:
        # Try to find user in auth.users
        users = await client.auth.admin.list_users()

        for user in users:
            if user.email and user.email.lower() == email.lower():
                logger.info(f"Found user {user.id} with email {email}")
                return user

        logger.warning(f"User with email {email} not found in auth.users")
        return None

    except Exception as e:
        logger.error(f"Error finding user by email {email}: {str(e)}")
        return None


async def get_user_account_id(client, user_id: str):
    """Get the account ID for a user (their personal account)."""
    try:
        result = (
            await client.schema("basejump")
            .from_("accounts")
            .select("id")
            .eq("primary_owner_user_id", user_id)
            .eq("personal_account", True)
            .execute()
        )

        if result.data and len(result.data) > 0:
            return result.data[0]["id"]
        return None

    except Exception as e:
        logger.error(f"Error getting account ID for user {user_id}: {str(e)}")
        return None


async def check_existing_billing_customer(client, account_id: str):
    """Check if user already has a billing customer record."""
    try:
        result = (
            await client.schema("basejump")
            .from_("billing_customers")
            .select("id, email, active")
            .eq("account_id", account_id)
            .execute()
        )

        if result.data and len(result.data) > 0:
            return result.data[0]
        return None

    except Exception as e:
        logger.error(f"Error checking existing billing customer: {str(e)}")
        return None


async def verify_stripe_customer(customer_id: str):
    """Verify that the Stripe customer exists."""
    try:
        customer = stripe.Customer.retrieve(customer_id)
        logger.info(f"Verified Stripe customer: {customer.email}")
        return customer
    except Exception as e:
        logger.error(f"Error verifying Stripe customer {customer_id}: {str(e)}")
        return None


async def create_discount_coupon():
    """Create a 100% discount coupon for free subscriptions."""
    try:
        # Check if coupon already exists
        try:
            coupon = stripe.Coupon.retrieve("MIGRATION_100_OFF")
            logger.info("Using existing 100% discount coupon")
            return coupon.id
        except stripe.error.InvalidRequestError:
            # Coupon doesn't exist, create it
            pass

        coupon = stripe.Coupon.create(
            id="MIGRATION_100_OFF",
            percent_off=100,
            duration="forever",
            name="Migration 100% Discount",
            metadata={"purpose": "migration"},
        )
        logger.info(f"Created 100% discount coupon: {coupon.id}")
        return coupon.id

    except Exception as e:
        logger.error(f"Error creating discount coupon: {str(e)}")
        return None


async def link_stripe_customer_to_user(
    client, account_id: str, customer_id: str, email: str
):
    """Link the existing Stripe customer to the user account."""
    try:
        # Insert or update the billing customer record
        customer_data = {
            "id": customer_id,
            "account_id": account_id,
            "email": email,
            "active": True,
            "provider": "stripe",
        }

        result = (
            await client.schema("basejump")
            .from_("billing_customers")
            .upsert(customer_data)
            .execute()
        )

        logger.info(f"Linked Stripe customer {customer_id} to account {account_id}")
        return True

    except Exception as e:
        logger.error(f"Error linking Stripe customer: {str(e)}")
        return False


async def create_pro_subscription(customer_id: str, email: str):
    """Create a free pro subscription for the customer."""
    try:
        # Check if customer already has an active pro subscription
        existing_subscriptions = stripe.Subscription.list(
            customer=customer_id, status="active", price=config.STRIPE_PRO_75_ID
        )

        if existing_subscriptions.data:
            logger.info(
                f"Customer {customer_id} already has an active pro subscription"
            )
            return existing_subscriptions.data[0]

        # Create discount coupon
        coupon_id = await create_discount_coupon()
        if not coupon_id:
            logger.error("Failed to create discount coupon")
            return None

        # Create free subscription
        subscription = stripe.Subscription.create(
            customer=customer_id,
            items=[{"price": config.STRIPE_PRO_75_ID}],
            discounts=[{"coupon": coupon_id}],
            metadata={
                "migration": "true",
                "migration_date": datetime.now(timezone.utc).isoformat(),
                "migration_reason": "Manual upgrade to pro plan",
                "user_email": email,
            },
        )

        logger.info(f"Created free pro subscription {subscription.id} for {email}")
        return subscription

    except Exception as e:
        logger.error(f"Error creating pro subscription: {str(e)}")
        return None


async def update_database_subscription(
    client, account_id: str, customer_id: str, subscription
):
    """Update the database with the subscription information."""
    try:
        subscription_data = {
            "id": subscription.id,
            "account_id": account_id,
            "billing_customer_id": customer_id,
            "status": subscription.status,
            "price_id": config.STRIPE_PRO_75_ID,
            "plan_name": "pro_75",
            "quantity": 1,
            "cancel_at_period_end": subscription.cancel_at_period_end,
            "current_period_start": datetime.fromtimestamp(
                subscription.current_period_start, tz=timezone.utc
            ),
            "current_period_end": datetime.fromtimestamp(
                subscription.current_period_end, tz=timezone.utc
            ),
            "metadata": subscription.metadata or {},
        }

        # Use upsert to handle existing subscriptions
        result = (
            await client.schema("basejump")
            .from_("billing_subscriptions")
            .upsert(subscription_data)
            .execute()
        )

        logger.info(f"Updated database with subscription {subscription.id}")
        return True

    except Exception as e:
        logger.error(f"Error updating database subscription: {str(e)}")
        return False


async def link_user_to_stripe_customer(email: str, stripe_customer_id: str):
    """Main function to link user to existing Stripe customer and upgrade to pro."""
    logger.info(
        f"Starting process to link {email} to Stripe customer {stripe_customer_id}"
    )

    try:
        # Initialize database connection
        db = DBConnection()
        client = await db.client

        # 1. Find user by email
        user = await find_user_by_email(client, email)
        if not user:
            logger.error(
                f"User with email {email} not found. They need to sign up first."
            )
            return False

        user_id = user.id
        logger.info(f"Found user: {user_id}")

        # 2. Get user's account ID
        account_id = await get_user_account_id(client, user_id)
        if not account_id:
            logger.error(f"No account found for user {user_id}")
            return False

        logger.info(f"Found account: {account_id}")

        # 3. Verify Stripe customer exists
        stripe_customer = await verify_stripe_customer(stripe_customer_id)
        if not stripe_customer:
            logger.error(f"Stripe customer {stripe_customer_id} not found")
            return False

        # 4. Check if user already has a billing customer
        existing_customer = await check_existing_billing_customer(client, account_id)
        if existing_customer:
            logger.warning(
                f"User already has billing customer: {existing_customer['id']}"
            )
            if existing_customer["id"] == stripe_customer_id:
                logger.info("User is already linked to this Stripe customer")
            else:
                logger.warning(
                    "User is linked to a different Stripe customer. Updating..."
                )

        # 5. Link Stripe customer to user account
        success = await link_stripe_customer_to_user(
            client, account_id, stripe_customer_id, email
        )
        if not success:
            logger.error("Failed to link Stripe customer to user")
            return False

        # 6. Create pro subscription
        subscription = await create_pro_subscription(stripe_customer_id, email)
        if not subscription:
            logger.error("Failed to create pro subscription")
            return False

        # 7. Update database with subscription
        success = await update_database_subscription(
            client, account_id, stripe_customer_id, subscription
        )
        if not success:
            logger.error("Failed to update database with subscription")
            return False

        logger.info(
            f"✅ Successfully linked {email} to Stripe customer {stripe_customer_id} and upgraded to pro!"
        )
        return True

    except Exception as e:
        logger.error(f"Error in main process: {str(e)}")
        return False


async def main():
    """Main entry point."""
    if len(sys.argv) != 3:
        print(
            "Usage: python link_existing_stripe_customer.py <email> <stripe_customer_id>"
        )
        print(
            "Example: python link_existing_stripe_customer.py <EMAIL> cus_1234567890"
        )
        sys.exit(1)

    email = sys.argv[1]
    stripe_customer_id = sys.argv[2]

    print(f"Linking {email} to Stripe customer {stripe_customer_id}...")

    success = await link_user_to_stripe_customer(email, stripe_customer_id)

    if success:
        print(
            f"✅ Successfully completed! {email} is now linked to {stripe_customer_id} with pro access."
        )
    else:
        print(f"❌ Failed to link {email} to {stripe_customer_id}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
