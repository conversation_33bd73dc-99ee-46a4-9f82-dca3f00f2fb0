import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogTitle,
} from '@/components/ui/dialog';
import { useMediaQuery } from '@/hooks/use-media-query';
import Image from 'next/image';
import Cal, { getCalApi } from '@calcom/embed-react';
import { useTheme } from 'next-themes';
import { KortixLogo } from '@/components/sidebar/kortix-logo';
import { Play, BookOpen, Calendar, Users } from 'lucide-react';

interface KortixProcessModalProps {
  open: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

type ActionType = 'how-to-use' | 'book-call' | 'community';

export function KortixProcessModal() {
  const [open, setOpen] = useState(false);
  const [activeAction, setActiveAction] = useState<ActionType>('how-to-use');
  const [isPlaying, setIsPlaying] = useState(false);
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === 'dark';

  useEffect(() => {
    (async function () {
      // No need for Cal API namespace for direct link embed
    })();
  }, []);

  const handlePlay = () => {
    setIsPlaying(true);
  };

  const handleCommunityClick = () => {
    window.open('https://discord.gg/nsxm8eep7d', '_blank');
  };

  const actionItems = [
    {
      id: 'how-to-use' as ActionType,
      title: 'How to use',
      description: 'Learn how to use the platform',
      icon: BookOpen,
      onClick: () => setActiveAction('how-to-use'),
    },
    {
      id: 'book-call' as ActionType,
      title: 'Book a call',
      description: 'For enterprise use',
      icon: Calendar,
      onClick: () => setActiveAction('book-call'),
    },
    {
      id: 'community' as ActionType,
      title: 'Join the community',
      description: 'Get help and support',
      icon: Users,
      onClick: handleCommunityClick,
    },
  ];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default" size="sm" className="w-full text-xs">
          Learn More
        </Button>
      </DialogTrigger>
      <DialogContent className="p-0 gap-0 border-none max-w-[70vw] rounded-xl overflow-hidden">
        <DialogTitle className="sr-only">
          Atlas: AI Agents for Workflow Automation
        </DialogTitle>
        <div className="grid grid-cols-1 md:grid-cols-2 h-[800px]">
          {/* Info Panel */}
          <div className="p-8 flex flex-col bg-white dark:bg-black relative h-full overflow-y-auto border-r border-gray-200 dark:border-gray-800">
            <div className="relative z-10 flex flex-col h-full">
              <div className="mb-8 mt-0 flex-shrink-0">
                <KortixLogo />
              </div>

              <h2 className="text-2xl md:text-3xl font-semibold tracking-tight mb-4 text-foreground flex-shrink-0">
                Atlas: AI Agents for Workflow Automation
              </h2>
              <p className="text-base md:text-lg text-muted-foreground mb-8 max-w-lg flex-shrink-0">
                Atlas deploys AI agents that automate your business workflows, integrate with your tools, and execute tasks 24/7. Boost productivity, reduce manual work, and scale your operations with intelligent automation.
              </p>

              {/* Action Items */}
              <div className="space-y-3 mb-auto flex-shrink-0">
                {actionItems.map((item, index) => {
                  const Icon = item.icon;
                  const isActive = activeAction === item.id && item.id !== 'community';
                  const isCommunity = item.id === 'community';
                  return (
                    <button
                      key={item.id}
                      onClick={item.onClick}
                      className={`w-full text-left p-4 rounded-xl border-2 transition-all duration-200 hover:scale-[1.02] group ${
                        isActive 
                          ? 'bg-primary/5 border-primary shadow-sm' 
                          : 'bg-card border-border hover:border-primary/30 hover:bg-accent/50'
                      }`}
                    >
                      <div className="flex items-center">
                        <div className={`w-12 h-12 rounded-xl inline-flex items-center justify-center mr-4 transition-all duration-200 ${
                          isActive 
                            ? 'bg-primary text-primary-foreground shadow-lg scale-110' 
                            : 'bg-muted text-muted-foreground group-hover:bg-primary/10 group-hover:text-primary group-hover:scale-105'
                        }`}>
                          <Icon className="h-5 w-5" />
                        </div>
                        <div className="flex-1">
                          <h3 className={`text-base font-semibold mb-1 transition-colors ${
                            isActive 
                              ? 'text-primary' 
                              : 'text-foreground group-hover:text-primary'
                          }`}>
                            {item.title}
                          </h3>
                          <p className={`text-sm transition-colors ${
                            isActive 
                              ? 'text-primary/70' 
                              : 'text-muted-foreground'
                          }`}>
                            {item.description}
                          </p>
                        </div>
                        {isCommunity && (
                          <div className="text-muted-foreground opacity-60 group-hover:opacity-100 group-hover:text-primary transition-all">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                          </div>
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
          
          {/* Right Panel - Conditional Content */}
          <div className="bg-white dark:bg-[#171717] h-full overflow-hidden">
            {activeAction === 'how-to-use' && (
              <div className="bg-black h-full overflow-hidden">
                <div className="relative w-full h-full">
                  {!isPlaying ? (
                    <div 
                      className="absolute inset-0 flex items-center justify-center bg-black cursor-pointer"
                      onClick={handlePlay}
                    >
                      <Image
                        src="https://img.youtube.com/vi/d8URSH1e19E/maxresdefault.jpg"
                        alt="Video thumbnail"
                        fill
                        className="object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = "https://img.youtube.com/vi/d8URSH1e19E/hqdefault.jpg";
                        }}
                      />
                      <div className="absolute inset-0 bg-black/30" />
                      <div className="relative z-10 flex items-center justify-center w-20 h-20 bg-primary/90 hover:bg-primary rounded-full shadow-lg transition-colors duration-200">
                        <Play className="h-8 w-8 text-primary-foreground ml-1" fill="currentColor" />
                      </div>
                      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white text-sm font-medium bg-black/50 px-3 py-1 rounded-full">
                        Click to play
                      </div>
                    </div>
                  ) : (
                    <iframe
                      src="https://www.youtube.com/embed/d8URSH1e19E?autoplay=1&rel=0&modestbranding=1&playsinline=1"
                      title="Short coffee-chat with Atlas, your new co-worker"
                      className="w-full h-full"
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                      allowFullScreen
                    />
                  )}
                </div>
              </div>
            )}
            
            {activeAction === 'book-call' && (
              <div className="h-full overflow-auto">
                <iframe
                  src="https://cal.com/atlasagents/15min?overlayCalendar=true"
                  style={{ width: '100%', height: '100%', border: 'none' }}
                  title="Book a meeting with Atlas"
                  allow="camera; microphone; fullscreen; speaker; display-capture"
                />
              </div>
            )}
            
            {/* Default state when no specific panel is needed */}
            {activeAction !== 'how-to-use' && activeAction !== 'book-call' && (
              <div className="h-full flex items-center justify-center p-8">
                <div className="text-center max-w-md">
                  <div className="w-20 h-20 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
                    <BookOpen className="h-10 w-10 text-muted-foreground" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4 text-foreground">
                    Welcome to Atlas
                  </h3>
                  <p className="text-muted-foreground">
                    Select an option from the left to get started with Atlas automation platform.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
