'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, Trash2, Plus, Check } from 'lucide-react';
import { ComposioApp } from '@/types/composio';
import { getComposioAppIcon } from '@/lib/icon-mapping';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface MCPServerCardProps {
  app: ComposioApp;
  isConnected: boolean;
  isConnecting: boolean;
  onConnect: () => void;
  onDisconnect: () => void;
  onViewTools?: () => void;
  isLoadingTools?: boolean;
}

export function MCPServerCard({
  app,
  isConnected,
  isConnecting,
  onConnect,
  onDisconnect,
  onViewTools,
  isLoadingTools = false
}: MCPServerCardProps) {
  const isMobile = useIsMobile();

  return (
    <motion.div
      whileHover={{
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      className="flex-shrink-0"
    >
      <Card
        className={cn(
          "cursor-pointer transition-all duration-200 shadow-none dark:shadow-sm dark:hover:shadow-lg bg-transparent dark:bg-muted/30 border border-border",
          isConnected ? (isMobile ? 'w-16' : 'w-20') : (isMobile ? 'w-36' : 'w-44'),
          isMobile ? 'h-16' : 'h-14'
        )}
        onClick={
          isConnected && onViewTools
            ? onViewTools
            : (!isConnected && !isConnecting ? onConnect : undefined)
        }
      >
        <CardContent className="h-full flex items-center justify-between px-2">
          {/* Left: Icon + Text */}
          <div className={`flex items-center min-w-0 flex-1 ${isConnected ? 'gap-2' : 'gap-3'}`}>
            {/* App Icon - Using shared icon mapping */}
            <div className="flex-shrink-0 w-9 h-9">
              {(() => {
                const IconComponent = getComposioAppIcon(app);
                return <IconComponent className="w-full h-full" />;
              })()}
            </div>

            {/* App Text - Only show when not connected */}
            {!isConnected && (
              <div className="min-w-0 flex-1">
                <div className="font-medium text-sm text-foreground truncate leading-tight">
                  {app.name}
                </div>
              </div>
            )}
          </div>

          {/* Right: Status/Actions - Properly centered */}
          <div className="flex items-center flex-shrink-0 ml-0">
            {isConnected ? (
              /* Connected state: Green check that becomes trash on hover, or loading spinner */
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onDisconnect();
                }}
                className="h-7 w-7 p-0 rounded-full text-green-500 hover:text-destructive hover:bg-destructive/10 group"
                disabled={isLoadingTools}
              >
                {isLoadingTools ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <>
                    <Check className="h-4 w-4 group-hover:hidden" />
                    <Trash2 className="h-4 w-4 hidden group-hover:block" />
                  </>
                )}
              </Button>
            ) : (
              /* Connect button with plus icon */
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onConnect();
                }}
                disabled={isConnecting}
                className="h-7 w-7 p-0 rounded-full hover:bg-primary/5"
              >
                {isConnecting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Plus className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
